# CHANGELOG 6.X

## 6.1.0

### Features

- [c60c828](https://github.com/dubzzz/pure-rand/commit/c60c828) ✨ Clone from state on `xorshift128plus` (#697)
- [6a16bfe](https://github.com/dubzzz/pure-rand/commit/6a16bfe) ✨ Clone from state on `mersenne` (#698)
- [fb78e2d](https://github.com/dubzzz/pure-rand/commit/fb78e2d) ✨ Clone from state on `xoroshiro128plus` (#699)
- [a7dd56c](https://github.com/dubzzz/pure-rand/commit/a7dd56c) ✨ Clone from state on congruential32 (#696)
- [1f6c3a5](https://github.com/dubzzz/pure-rand/commit/1f6c3a5) 🏷️ Expose internal state of generators (#694)

### Fixes

- [30d439a](https://github.com/dubzzz/pure-rand/commit/30d439a) 💚 Fix broken lock file (#695)
- [9f935ae](https://github.com/dubzzz/pure-rand/commit/9f935ae) 👷 Speed-up CI with better cache (#677)

## 6.0.4

### Fixes

- [716e073](https://github.com/dubzzz/pure-rand/commit/716e073) 🐛 Fix typings for node native esm (#649)

## 6.0.3

### Fixes

- [9aca792](https://github.com/dubzzz/pure-rand/commit/9aca792) 🏷️ Better declare ESM's types (#634)

## 6.0.2

### Fixes

- [6d05e8f](https://github.com/dubzzz/pure-rand/commit/6d05e8f) 🔐 Sign published packages (#591)
- [8b4e165](https://github.com/dubzzz/pure-rand/commit/8b4e165) 👷 Switch default to Node 18 in CI (#578)

## 6.0.1

### Fixes

- [05421f2](https://github.com/dubzzz/pure-rand/commit/05421f2) 🚨 Reformat README.md (#563)
- [ffacfbd](https://github.com/dubzzz/pure-rand/commit/ffacfbd) 📝 Give simple seed computation example (#562)
- [e432d59](https://github.com/dubzzz/pure-rand/commit/e432d59) 📝 Add extra keywords (#561)
- [f5b18d4](https://github.com/dubzzz/pure-rand/commit/f5b18d4) 🐛 Declare types first for package (#560)
- [a5b30db](https://github.com/dubzzz/pure-rand/commit/a5b30db) 📝 Final clean-up of the README (#559)
- [5254ee0](https://github.com/dubzzz/pure-rand/commit/5254ee0) 📝 Fix simple examples not fully working (#558)
- [8daf460](https://github.com/dubzzz/pure-rand/commit/8daf460) 📝 Clarify the README (#556)
- [a915b6a](https://github.com/dubzzz/pure-rand/commit/a915b6a) 📝 Fix url error in README for logo (#554)
- [f94885c](https://github.com/dubzzz/pure-rand/commit/f94885c) 📝 Rework README header with logo (#553)
- [5f7645e](https://github.com/dubzzz/pure-rand/commit/5f7645e) 📝 Typo in link to comparison SVG (#551)
- [61726af](https://github.com/dubzzz/pure-rand/commit/61726af) 📝 Better keywords for NPM (#550)
- [6001e5a](https://github.com/dubzzz/pure-rand/commit/6001e5a) 📝 Update performance section with recent stats (#549)
- [556ec33](https://github.com/dubzzz/pure-rand/commit/556ec33) ⚗️ Rewrite not uniform of pure-rand (#547)
- [b3dfea5](https://github.com/dubzzz/pure-rand/commit/b3dfea5) ⚗️ Add more libraries to the experiment (#546)
- [ac8b85d](https://github.com/dubzzz/pure-rand/commit/ac8b85d) ⚗️ Add some more non-uniform versions (#543)
- [44af2ad](https://github.com/dubzzz/pure-rand/commit/44af2ad) ⚗️ Add some more self comparisons (#542)
- [6d3342d](https://github.com/dubzzz/pure-rand/commit/6d3342d) 📝 Add some more details on the algorithms in compare (#541)
- [359e214](https://github.com/dubzzz/pure-rand/commit/359e214) 📝 Fix some typos in README (#540)
- [28a7bfe](https://github.com/dubzzz/pure-rand/commit/28a7bfe) 📝 Document some performance stats (#539)
- [81860b7](https://github.com/dubzzz/pure-rand/commit/81860b7) ⚗️ Measure performance against other libraries (#538)
- [114c2c7](https://github.com/dubzzz/pure-rand/commit/114c2c7) 📝 Publish changelogs from 3.X to 6.X (#537)

## 6.0.0

### Breaking Changes

- [c45912f](https://github.com/dubzzz/pure-rand/commit/c45912f) 💥 Require generators uniform in int32 (#513)
- [0bde03e](https://github.com/dubzzz/pure-rand/commit/0bde03e) 💥 Drop congruencial generator (#511)

### Features

- [7587984](https://github.com/dubzzz/pure-rand/commit/7587984) ⚡️ Faster uniform distribution on bigint (#517)
- [464960a](https://github.com/dubzzz/pure-rand/commit/464960a) ⚡️ Faster uniform distribution on small ranges (#516)
- [b4852a8](https://github.com/dubzzz/pure-rand/commit/b4852a8) ⚡️ Faster Congruencial 32bits (#512)
- [fdb6ec8](https://github.com/dubzzz/pure-rand/commit/fdb6ec8) ⚡️ Faster Mersenne-Twister (#510)
- [bb69be5](https://github.com/dubzzz/pure-rand/commit/bb69be5) ⚡️ Drop infinite loop for explicit loop (#507)

### Fixes

- [00fc62b](https://github.com/dubzzz/pure-rand/commit/00fc62b) 🔨 Add missing benchType to the script (#522)
- [db4a0a6](https://github.com/dubzzz/pure-rand/commit/db4a0a6) 🔨 Add more options to benchmark (#521)
- [5c1ca0e](https://github.com/dubzzz/pure-rand/commit/5c1ca0e) 🔨 Fix typo in benchmark code (#520)
- [36c965f](https://github.com/dubzzz/pure-rand/commit/36c965f) 👷 Define a benchmark workflow (#519)
- [0281cfd](https://github.com/dubzzz/pure-rand/commit/0281cfd) 🔨 More customizable benchmark (#518)
- [a7e19a8](https://github.com/dubzzz/pure-rand/commit/a7e19a8) 🔥 Clean internals of uniform distribution (#515)
- [520cca7](https://github.com/dubzzz/pure-rand/commit/520cca7) 🔨 Add some more benchmarks (#514)
- [c2d6ee6](https://github.com/dubzzz/pure-rand/commit/c2d6ee6) 🔨 Fix typo in bench for large reference (#509)
- [2dd7280](https://github.com/dubzzz/pure-rand/commit/2dd7280) 🔥 Clean useless variable (#506)
- [dd621c9](https://github.com/dubzzz/pure-rand/commit/dd621c9) 🔨 Adapt benchmarks to make them reliable (#505)
- [122f968](https://github.com/dubzzz/pure-rand/commit/122f968) 👷 Drop dependabot
- [f11d2e8](https://github.com/dubzzz/pure-rand/commit/f11d2e8) 💸 Add GitHub sponsors in repository's configuration
- [6a23e48](https://github.com/dubzzz/pure-rand/commit/6a23e48) 👷 Stop running tests against node 12 (#486)
- [cbefd3e](https://github.com/dubzzz/pure-rand/commit/cbefd3e) 🔧 Better configuration of prettier (#474)
- [c6712d3](https://github.com/dubzzz/pure-rand/commit/c6712d3) 🔧 Configure Renovate (#470)
